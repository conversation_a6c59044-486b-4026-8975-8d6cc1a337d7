name: imtrans
description: "Combining machine translation with OCR graphic and text recognition, it supports recognition of image text content and automatically translates image text into the corresponding language based on options. The output results support backfilling into the image."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 2.4.0+0

environment:
  sdk: '>=3.4.0-190.1.beta <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: any

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  webview_flutter: ^4.8.0
  path_provider: ^2.1.4
  shared_preferences: ^2.2.2
  http: ^1.2.1
  crypto: ^3.0.3
  cached_network_image: ^3.3.1
  wave: ^0.2.2
  event_bus: ^2.0.0
  clipboard: any
  image_picker: ^1.1.2
  gal: ^2.3.1
  device_info_plus: ^11.2.2  
  fluttertoast: ^8.2.8
  bootstrap_icons: ^1.11.3
  archive: ^4.0.2
  file_picker: ^8.0.5
  sqflite: ^2.3.3+1
  # flutter_email_sender: ^6.0.3
  flutter_native_splash: ^2.4.1
  path: any
  http_parser: any
  flutter_cache_manager: any
  camera: ^0.11.0+2
  flutter_staggered_grid_view: ^0.7.0
  provider: ^6.0.2
  image: ^4.5.2
  flutter_image_compress: ^2.3.0
  flutter_svg: ^2.0.9
  package_info_plus: any
  flutter_inappwebview: ^6.1.5
  flutter_launcher_icons: ^0.14.4 
  vision_text_recognition: ^1.0.2
  google_mlkit_translation: ^0.13.0
  google_mlkit_language_id: ^0.13.0

  # account
  uuid: ^4.4.2
  sign_in_with_apple: ^6.1.4
  auth0_flutter: ^1.9.0
  flutter_secure_storage: ^9.2.4
  in_app_purchase: ^3.2.1
  
  # AD
  # google_mobile_ads: ^6.0.0

  # fonts
  google_fonts: ^6.2.1

  # for firebase
  cloud_firestore: ^5.6.6
  firebase_auth: ^5.6.0
  firebase_core: ^3.13.0
  google_sign_in: ^6.3.0
  firebase_analytics: ^11.4.5

  # for attribution and analytics
  appsflyer_sdk: ^6.15.1

  # for test
  mockito: any
  test: any
  # build_runner: ^2.4.8


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.

flutter:
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    # 基础图片资源
    - images/logo.png
    - images/nodata_line.png
    - images/login.png
    - images/bg_protab.png
    - images/bg_protext.png
    - images/draft_nodata.png

    # 白色主题图标
    - images/white/
    
    # 黑色主题图标
    - images/black/
    
    # 启动页面图片
    - images/launch/
    
    # 支付相关图片
    - images/pay/
    
    # 引导页面图片
    - images/guide/

    # 通用ICON
    - images/icons/

    # JavaScript 资源
    - assets/js/
    - assets/js/templates/

    - assets/easylist.txt


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Poppins
      fonts:
        - asset: images/fonts/Poppins/Poppins-Thin.ttf
          weight: 100
        - asset: images/fonts/Poppins/Poppins-ExtraLight.ttf
          weight: 200
        - asset: images/fonts/Poppins/Poppins-Light.ttf
          weight: 300
        - asset: images/fonts/Poppins/Poppins-Regular.ttf
          weight: 400
        - asset: images/fonts/Poppins/Poppins-Medium.ttf
          weight: 500
        - asset: images/fonts/Poppins/Poppins-SemiBold.ttf
          weight: 600
        - asset: images/fonts/Poppins/Poppins-Bold.ttf
          weight: 700
        - asset: images/fonts/Poppins/Poppins-ExtraBold.ttf
          weight: 800
        - asset: images/fonts/Poppins/Poppins-Black.ttf
          weight: 900

        - asset: images/fonts/Poppins/Poppins-ThinItalic.ttf
          weight: 100
          style: italic
        - asset: images/fonts/Poppins/Poppins-ExtraLightItalic.ttf
          weight: 200
          style: italic
        - asset: images/fonts/Poppins/Poppins-LightItalic.ttf
          weight: 300
          style: italic
        - asset: images/fonts/Poppins/Poppins-Italic.ttf
          weight: 400
          style: italic
        - asset: images/fonts/Poppins/Poppins-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: images/fonts/Poppins/Poppins-SemiBoldItalic.ttf
          weight: 600
          style: italic
        - asset: images/fonts/Poppins/Poppins-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: images/fonts/Poppins/Poppins-ExtraBoldItalic.ttf
          weight: 800
          style: italic
        - asset: images/fonts/Poppins/Poppins-BlackItalic.ttf
          weight: 900
          style: italic

    # Comic Neue fonts
    - family: ComicNeue
      fonts:
        # 常规字体
        - asset: images/fonts/Comic_Neue/ComicNeue-Light.ttf
          weight: 300
        - asset: images/fonts/Comic_Neue/ComicNeue-Regular.ttf
          weight: 400
        - asset: images/fonts/Comic_Neue/ComicNeue-Bold.ttf
          weight: 700
        
          # 斜体字体
        - asset: images/fonts/Comic_Neue/ComicNeue-LightItalic.ttf
          weight: 300
          style: italic
        - asset: images/fonts/Comic_Neue/ComicNeue-Italic.ttf
          weight: 400
          style: italic
        - asset: images/fonts/Comic_Neue/ComicNeue-BoldItalic.ttf
          weight: 700
          style: italic

    # Inter fonts
    - family: Inter
      fonts:
        - asset: images/fonts/Inter/Inter-VariableFont_opsz,wght.ttf

        - asset: images/fonts/Inter/Inter-Italic-VariableFont_opsz,wght.ttf
          style: italic

    # Bangers font
    - family: Bangers
      fonts:
        - asset: images/fonts/Bangers/Bangers-Regular.ttf

    # Dela Gothic One
    - family: Dela Gothic One
      fonts:
        - asset: images/fonts/Dela_Gothic_One/DelaGothicOne-Regular.ttf

    # M PLUS Rounded 1c
    - family: M PLUS Rounded 1c
      fonts:
        - asset: images/fonts/MPLUSRounded1c/MPLUSRounded1c-Thin.ttf
          weight: 100
        - asset: images/fonts/MPLUSRounded1c/MPLUSRounded1c-Light.ttf
          weight: 300
        - asset: images/fonts/MPLUSRounded1c/MPLUSRounded1c-Regular.ttf
          weight: 400
        - asset: images/fonts/MPLUSRounded1c/MPLUSRounded1c-Medium.ttf
          weight: 500
        - asset: images/fonts/MPLUSRounded1c/MPLUSRounded1c-Bold.ttf
          weight: 700
        - asset: images/fonts/MPLUSRounded1c/MPLUSRounded1c-ExtraBold.ttf
          weight: 800
        - asset: images/fonts/MPLUSRounded1c/MPLUSRounded1c-Black.ttf
          weight: 900

    # Roboto Condensed
    - family: Roboto Condensed
      fonts:
        - asset: images/fonts/Roboto_Condensed/RobotoCondensed-VariableFont_wght.ttf

        - asset: images/fonts/Roboto_Condensed/RobotoCondensed-Italic-VariableFont_wght.ttf
          style: italic

    # Shippori Mincho
    - family: Shippori Mincho
      fonts:
        - asset: images/fonts/ShipporiMincho/ShipporiMincho-Regular.ttf
          weight: 400
        - asset: images/fonts/ShipporiMincho/ShipporiMincho-Medium.ttf
          weight: 500
        - asset: images/fonts/ShipporiMincho/ShipporiMincho-SemiBold.ttf
          weight: 600
        - asset: images/fonts/ShipporiMincho/ShipporiMincho-Bold.ttf
          weight: 700
        - asset: images/fonts/ShipporiMincho/ShipporiMincho-ExtraBold.ttf
          weight: 800

    # Anime Ace
    - family: Anime Ace
      fonts:
        - asset: images/fonts/animeace3bb_ot/AnimeAce3BB_Regular.otf
        - asset: images/fonts/animeace3bb_ot/AnimeAce3BB_Bold.otf
          weight: 700
        - asset: images/fonts/animeace3bb_ot/AnimeAce3BB_Italic.otf
          style: italic

    # Komika Title
    - family: Komika Title
      fonts:
        - asset: images/fonts/komika_title/KOMTIT__.ttf

    # Manga Temple
    - family: Manga Temple
      fonts:
        - asset: images/fonts/manga_temple/mangat.ttf
        - asset: images/fonts/manga_temple/mangatb.ttf
          weight: 700
        - asset: images/fonts/manga_temple/mangati.ttf
          style: italic

    # Pretendard-1 Korean
    - family: Pretendard
      fonts:
        - asset: images/fonts/Pretendard-1/PretendardVariable.ttf
          
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

flutter_launcher_icons:
  android: true
  image_path: "images/avatar_imtrans_400.png"