import 'package:flutter_test/flutter_test.dart';

/// MangaDex图片检测修复测试
/// 
/// 验证修复后的图片过滤逻辑能够正确识别MangaDex页面上的漫画图片
void main() {
  group('MangaDex图片检测修复测试', () {
    
    test('漫画网站应该跳过视口位置检查', () async {
      // 验证修复：漫画网站不应该基于视口位置过滤图片
      const mangaSites = [
        'mangadex.org',
        'mangadx.org',
        'manga.example.com',
        'comic.example.com',
      ];
      
      const nonMangaSites = [
        'google.com',
        'facebook.com',
        'news.example.com',
      ];
      
      // 验证漫画网站检测逻辑
      for (final site in mangaSites) {
        expect(site.contains('manga') || site.contains('comic'), isTrue,
               reason: '$site 应该被识别为漫画网站');
      }
      
      for (final site in nonMangaSites) {
        expect(site.contains('manga') || site.contains('comic'), isFalse,
               reason: '$site 不应该被识别为漫画网站');
      }
    });

    test('高分辨率图片不应该被过滤', () async {
      // 验证修复：移除了对大尺寸图片的过滤
      const highResImages = [
        {'width': 2500, 'height': 3500, 'shouldAllow': true, 'reason': '高分辨率漫画页'},
        {'width': 1800, 'height': 2400, 'shouldAllow': true, 'reason': '标准漫画页'},
        {'width': 4000, 'height': 6000, 'shouldAllow': true, 'reason': '超高分辨率漫画页'},
        {'width': 50, 'height': 50, 'shouldAllow': false, 'reason': '小图标'},
        {'width': 150, 'height': 150, 'shouldAllow': false, 'reason': '装饰图片'},
      ];
      
      for (final image in highResImages) {
        final width = image['width'] as int;
        final height = image['height'] as int;
        final shouldAllow = image['shouldAllow'] as bool;
        final reason = image['reason'] as String;
        
        // 验证尺寸过滤逻辑
        final isLargeEnough = width >= 200 && height >= 200;
        expect(isLargeEnough, equals(shouldAllow),
               reason: '$reason (${width}x${height}) 的过滤结果不正确');
      }
    });

    test('视口外的图片不应该被标记为背景图片', () async {
      // 验证修复：移除了对视口外图片的背景图片判断
      const viewportScenarios = [
        {
          'rect': {'top': -500, 'bottom': -100, 'left': 0, 'right': 800},
          'viewport': {'width': 800, 'height': 600},
          'isBackground': false,
          'reason': '视口上方的图片（漫画页面常见）',
        },
        {
          'rect': {'top': 1000, 'bottom': 1500, 'left': 0, 'right': 800},
          'viewport': {'width': 800, 'height': 600},
          'isBackground': false,
          'reason': '视口下方的图片（漫画页面常见）',
        },
        {
          'rect': {'top': 0, 'bottom': 600, 'left': 0, 'right': 800},
          'viewport': {'width': 800, 'height': 600},
          'isBackground': true,
          'reason': '覆盖整个视口的图片（可能是背景）',
        },
        {
          'rect': {'top': 0, 'bottom': 800, 'left': 0, 'right': 1000},
          'viewport': {'width': 800, 'height': 600},
          'isBackground': true,
          'reason': '超出视口的大图片（可能是背景）',
        },
      ];
      
      for (final scenario in viewportScenarios) {
        final rect = scenario['rect'] as Map<String, int>;
        final viewport = scenario['viewport'] as Map<String, int>;
        final expectedBackground = scenario['isBackground'] as bool;
        final reason = scenario['reason'] as String;
        
        // 验证背景图片判断逻辑（只基于尺寸，不基于位置）
        final coversViewport = rect['right']! - rect['left']! >= viewport['width']! &&
                              rect['bottom']! - rect['top']! >= viewport['height']!;
        
        expect(coversViewport, equals(expectedBackground),
               reason: '$reason 的背景图片判断不正确');
      }
    });

    test('MangaDex域名检测应该正确工作', () async {
      // 验证修复：正确的域名检测
      const testUrls = [
        {'hostname': 'mangadex.org', 'shouldMatch': true},
        {'hostname': 'www.mangadex.org', 'shouldMatch': true},
        {'hostname': 'mangadx.org', 'shouldMatch': true}, // 旧的拼写错误
        {'hostname': 'example.com', 'shouldMatch': false},
        {'hostname': 'google.com', 'shouldMatch': false},
      ];
      
      for (final testUrl in testUrls) {
        final hostname = testUrl['hostname'] as String;
        final shouldMatch = testUrl['shouldMatch'] as bool;
        
        final matches = hostname.contains('mangadex.org') || hostname.contains('mangadx.org');
        expect(matches, equals(shouldMatch),
               reason: '$hostname 的域名检测结果不正确');
      }
    });

    test('图片缩放比例检测应该合理', () async {
      // 验证修复：调整了缩放比例的阈值
      const scalingScenarios = [
        {
          'natural': {'width': 2000, 'height': 3000},
          'display': {'width': 400, 'height': 600},
          'isBackground': false,
          'reason': '正常缩放（20%）',
        },
        {
          'natural': {'width': 2000, 'height': 3000},
          'display': {'width': 100, 'height': 150},
          'isBackground': true,
          'reason': '过度缩放（5%，小于10%阈值）',
        },
        {
          'natural': {'width': 200, 'height': 300},
          'display': {'width': 2000, 'height': 3000},
          'isBackground': true,
          'reason': '过度放大（10倍）',
        },
        {
          'natural': {'width': 200, 'height': 300},
          'display': {'width': 1000, 'height': 1500},
          'isBackground': false,
          'reason': '合理放大（5倍）',
        },
      ];
      
      for (final scenario in scalingScenarios) {
        final natural = scenario['natural'] as Map<String, int>;
        final display = scenario['display'] as Map<String, int>;
        final expectedBackground = scenario['isBackground'] as bool;
        final reason = scenario['reason'] as String;
        
        final scaleX = display['width']! / natural['width']!;
        final scaleY = display['height']! / natural['height']!;
        
        // 验证缩放检测逻辑
        final isExtremeScaling = scaleX < 0.1 || scaleY < 0.1 || scaleX > 10 || scaleY > 10;
        
        expect(isExtremeScaling, equals(expectedBackground),
               reason: '$reason 的缩放检测不正确 (scaleX: $scaleX, scaleY: $scaleY)');
      }
    });

    test('CSS类名和样式检测应该准确', () async {
      // 验证CSS相关的背景图片检测
      const cssScenarios = [
        {
          'className': 'manga-page-image',
          'position': 'static',
          'zIndex': 'auto',
          'isBackground': false,
          'reason': '普通漫画页面图片',
        },
        {
          'className': 'background-image',
          'position': 'static',
          'zIndex': 'auto',
          'isBackground': true,
          'reason': '背景类名',
        },
        {
          'className': 'content-image',
          'position': 'fixed',
          'zIndex': 'auto',
          'isBackground': true,
          'reason': 'fixed定位',
        },
        {
          'className': 'content-image',
          'position': 'static',
          'zIndex': '-1',
          'isBackground': true,
          'reason': '负z-index',
        },
      ];
      
      for (final scenario in cssScenarios) {
        final className = scenario['className'] as String;
        final position = scenario['position'] as String;
        final zIndex = scenario['zIndex'] as String;
        final expectedBackground = scenario['isBackground'] as bool;
        final reason = scenario['reason'] as String;
        
        // 验证CSS检测逻辑
        final hasBackgroundClass = className.contains('background') ||
                                  className.contains('bg-') ||
                                  className.contains('backdrop');
        
        final hasBackgroundStyle = position == 'fixed' ||
                                  position == 'absolute' ||
                                  (zIndex != 'auto' && int.tryParse(zIndex) != null && int.parse(zIndex) < 0);
        
        final isBackground = hasBackgroundClass || hasBackgroundStyle;
        
        expect(isBackground, equals(expectedBackground),
               reason: '$reason 的CSS检测不正确');
      }
    });
  });

  group('图片过滤性能验证', () {
    test('过滤逻辑应该高效处理大量图片', () async {
      // 模拟大量图片的过滤场景
      const imageCount = 100;
      final images = List.generate(imageCount, (index) => {
        'src': 'blob:https://mangadex.org/image-$index',
        'width': 800 + (index % 5) * 100,
        'height': 1200 + (index % 5) * 100,
        'className': index % 10 == 0 ? 'background-image' : 'manga-page',
      });
      
      expect(images.length, equals(imageCount));
      
      // 验证过滤结果
      final validImages = images.where((img) {
        final width = img['width'] as int;
        final height = img['height'] as int;
        final className = img['className'] as String;
        
        // 尺寸检查
        if (width < 200 || height < 200) return false;
        
        // CSS类名检查
        if (className.contains('background')) return false;
        
        return true;
      }).toList();
      
      // 应该过滤掉10%的背景图片
      expect(validImages.length, equals(90));
    });

    test('漫画网站检测应该快速准确', () async {
      // 验证网站类型检测的性能和准确性
      const testCases = [
        {'url': 'https://mangadex.org/chapter/123', 'isManga': true},
        {'url': 'https://mangadx.org/read/456', 'isManga': true},
        {'url': 'https://manga.example.com/page/789', 'isManga': true},
        {'url': 'https://comic.site.com/chapter/abc', 'isManga': true},
        {'url': 'https://news.example.com/article/def', 'isManga': false},
        {'url': 'https://social.media.com/post/ghi', 'isManga': false},
      ];
      
      for (final testCase in testCases) {
        final url = testCase['url'] as String;
        final expectedManga = testCase['isManga'] as bool;
        
        final isManga = url.contains('manga') || url.contains('comic');
        expect(isManga, equals(expectedManga),
               reason: '$url 的网站类型检测不正确');
      }
    });
  });

  group('边界情况处理', () {
    test('异常图片数据应该安全处理', () async {
      // 验证异常情况的处理
      const edgeCases = [
        {'width': 0, 'height': 0, 'shouldAllow': false, 'reason': '零尺寸'},
        {'width': -1, 'height': 100, 'shouldAllow': false, 'reason': '负宽度'},
        {'width': 100, 'height': -1, 'shouldAllow': false, 'reason': '负高度'},
        {'width': null, 'height': 200, 'shouldAllow': false, 'reason': 'null宽度'},
        {'width': 200, 'height': null, 'shouldAllow': false, 'reason': 'null高度'},
      ];
      
      for (final edgeCase in edgeCases) {
        final width = edgeCase['width'];
        final height = edgeCase['height'];
        final shouldAllow = edgeCase['shouldAllow'] as bool;
        final reason = edgeCase['reason'] as String;
        
        // 安全的尺寸检查
        final safeWidth = (width is int && width > 0) ? width : 0;
        final safeHeight = (height is int && height > 0) ? height : 0;
        final isValidSize = safeWidth >= 200 && safeHeight >= 200;
        
        expect(isValidSize, equals(shouldAllow),
               reason: '$reason 的边界情况处理不正确');
      }
    });
  });
}
