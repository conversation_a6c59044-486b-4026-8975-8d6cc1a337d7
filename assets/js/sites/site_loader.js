/**
 * Site detection and configuration loading system
 * Handles dynamic loading of site-specific configurations
 */

class SiteLoader {
  constructor() {
    this.currentSite = null;
    this.loadedConfigs = new Map();
    this.defaultConfig = null;
    this.initialized = false;
  }

  /**
   * Initialize the site loader
   */
  async initialize() {
    if (this.initialized) return;

    console.log('SiteLoader: Initializing site detection system');
    
    // Load default configuration
    await this.loadDefaultConfig();
    
    // Detect and load current site configuration
    await this.detectAndLoadSite();
    
    this.initialized = true;
    console.log('SiteLoader: Initialization complete');
  }

  /**
   * Load the default site configuration
   */
  async loadDefaultConfig() {
    try {
      // Create default config instance
      this.defaultConfig = new SiteConfigBase();
      this.defaultConfig.siteName = 'default';
      this.defaultConfig.initialize();
      
      console.log('SiteLoader: Default configuration loaded');
    } catch (error) {
      console.error('SiteLoader: Failed to load default configuration:', error);
    }
  }

  /**
   * Detect current site and load appropriate configuration
   */
  async detectAndLoadSite() {
    const currentUrl = window.location.href;
    const hostname = window.location.hostname;
    
    console.log('SiteLoader: Detecting site for:', hostname);

    // Site detection patterns
    const sitePatterns = [
      {
        name: 'mangadex',
        patterns: [
          /mangadex\.org/i
        ],
        configFile: 'mangadex.js'
      }
      // Add more sites here as needed
    ];

    // Find matching site
    let matchedSite = null;
    for (const site of sitePatterns) {
      for (const pattern of site.patterns) {
        if (pattern.test(hostname) || pattern.test(currentUrl)) {
          matchedSite = site;
          break;
        }
      }
      if (matchedSite) break;
    }

    if (matchedSite) {
      console.log('SiteLoader: Detected site:', matchedSite.name);
      await this.loadSiteConfig(matchedSite);
    } else {
      console.log('SiteLoader: No specific site configuration found, using default');
      this.currentSite = this.defaultConfig;
    }
  }

  /**
   * Load site-specific configuration
   * @param {Object} siteInfo - Site information object
   */
  async loadSiteConfig(siteInfo) {
    try {
      // Check if already loaded
      if (this.loadedConfigs.has(siteInfo.name)) {
        this.currentSite = this.loadedConfigs.get(siteInfo.name);
        console.log('SiteLoader: Using cached configuration for:', siteInfo.name);
        return;
      }

      // Load site configuration dynamically
      console.log('SiteLoader: Loading configuration for:', siteInfo.name);

      // Handle MangaDx specifically
      if (siteInfo.name === 'mangadx') {
        if (window.MangaDxSiteConfig) {
          this.currentSite = new window.MangaDxSiteConfig();
          this.currentSite.initialize();
          this.loadedConfigs.set(siteInfo.name, this.currentSite);
          console.log('SiteLoader: MangaDx configuration loaded successfully');
        } else {
          console.warn('SiteLoader: MangaDx configuration class not found, using default');
          this.currentSite = this.defaultConfig;
        }
      }

      // Fallback to default if loading fails
      if (!this.currentSite) {
        console.log('SiteLoader: Falling back to default configuration');
        this.currentSite = this.defaultConfig;
      }

    } catch (error) {
      console.error('SiteLoader: Failed to load site configuration:', error);
      this.currentSite = this.defaultConfig;
    }
  }

  /**
   * Get the current site configuration
   * @returns {SiteConfigBase} - Current site configuration
   */
  getCurrentSite() {
    return this.currentSite || this.defaultConfig;
  }

  /**
   * Check if a specific site is currently loaded
   * @param {string} siteName - Name of the site to check
   * @returns {boolean} - True if the site is currently loaded
   */
  isCurrentSite(siteName) {
    return this.currentSite && this.currentSite.siteName === siteName;
  }

  /**
   * Reload site configuration (useful for development/debugging)
   */
  async reloadSite() {
    console.log('SiteLoader: Reloading site configuration');
    
    if (this.currentSite && this.currentSite !== this.defaultConfig) {
      this.currentSite.cleanup();
    }
    
    this.currentSite = null;
    await this.detectAndLoadSite();
  }

  /**
   * Get debug information about the current site
   * @returns {Object} - Debug information
   */
  getDebugInfo() {
    const currentSite = this.getCurrentSite();
    return {
      initialized: this.initialized,
      currentSiteName: currentSite ? currentSite.siteName : 'none',
      loadedConfigs: Array.from(this.loadedConfigs.keys()),
      hasDefaultConfig: !!this.defaultConfig,
      currentUrl: window.location.href,
      hostname: window.location.hostname,
      siteDebugInfo: currentSite ? currentSite.getDebugInfo() : null
    };
  }

  /**
   * Cleanup all loaded configurations
   */
  cleanup() {
    console.log('SiteLoader: Cleaning up all configurations');
    
    // Cleanup current site
    if (this.currentSite && this.currentSite !== this.defaultConfig) {
      this.currentSite.cleanup();
    }
    
    // Cleanup all loaded configs
    for (const config of this.loadedConfigs.values()) {
      if (config !== this.defaultConfig) {
        config.cleanup();
      }
    }
    
    // Cleanup default config
    if (this.defaultConfig) {
      this.defaultConfig.cleanup();
    }
    
    this.loadedConfigs.clear();
    this.currentSite = null;
    this.defaultConfig = null;
    this.initialized = false;
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SiteLoader;
} else {
  window.SiteLoader = SiteLoader;
}
